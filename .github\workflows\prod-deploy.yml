name: 🔒 Production Environment Deployment

on:
  workflow_dispatch:
    inputs:
      project_id:
        description: 'Project ID to deploy (e.g., ai-spring-backend)'
        required: true
        type: string
      release_tag:
        description: 'Release tag to deploy (must be a GitHub release, e.g., v1.0.0)'
        required: true
        type: string
      docker_tag:
        description: 'Docker image tag to deploy (defaults to release tag without v prefix)'
        required: false
        type: string
      deployment_reason:
        description: 'Reason for production deployment (required for audit)'
        required: true
        type: string
      rollback_plan:
        description: 'Rollback plan in case of deployment failure'
        required: true
        type: string

env:
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-production-request:
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      project-id: ${{ steps.validate.outputs.project-id }}
      release-tag: ${{ steps.validate.outputs.release-tag }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      application-type: ${{ steps.validate.outputs.application-type }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
      deployment-reason: ${{ steps.validate.outputs.deployment-reason }}
      rollback-plan: ${{ steps.validate.outputs.rollback-plan }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔒 Validate Production Deployment Request
        id: validate
        run: |
          echo "=== PRODUCTION DEPLOYMENT VALIDATION ==="
          echo "Triggered by: ${{ github.actor }}"
          echo "Project ID: ${{ github.event.inputs.project_id }}"
          echo "Release Tag: ${{ github.event.inputs.release_tag }}"
          echo "Docker Tag: ${{ github.event.inputs.docker_tag }}"
          echo "Deployment Reason: ${{ github.event.inputs.deployment_reason }}"
          echo "Rollback Plan: ${{ github.event.inputs.rollback_plan }}"
          echo "=================================="

          PROJECT_ID="${{ github.event.inputs.project_id }}"
          RELEASE_TAG="${{ github.event.inputs.release_tag }}"
          DOCKER_TAG="${{ github.event.inputs.docker_tag }}"
          DEPLOYMENT_REASON="${{ github.event.inputs.deployment_reason }}"
          ROLLBACK_PLAN="${{ github.event.inputs.rollback_plan }}"

          # Validate that user is authorized for production deployments
          AUTHORIZED_USERS="AshrafSyed25"
          if ! echo "$AUTHORIZED_USERS" | grep -q "${{ github.actor }}"; then
            echo "❌ Unauthorized user: ${{ github.actor }}"
            echo "Only the following users can deploy to production: $AUTHORIZED_USERS"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          echo "✅ User ${{ github.actor }} is authorized for production deployments"

          # Validate project ID format
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate release tag format (semantic versioning)
          if ! echo "$RELEASE_TAG" | grep -qE '^v?[0-9]+\.[0-9]+\.[0-9]+$'; then
            echo "❌ Invalid release tag format: $RELEASE_TAG"
            echo "Release tag must follow semantic versioning (e.g., v1.0.0, 1.2.3)"
            echo "Pre-release tags are not allowed in production"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Check if release tag exists as a GitHub release
          echo "🔍 Checking if GitHub release exists for tag: $RELEASE_TAG"
          RELEASE_CHECK=$(curl -s -H "Authorization: token ${{ secrets.GITOPS_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/releases/tags/$RELEASE_TAG")
          
          if echo "$RELEASE_CHECK" | grep -q '"message": "Not Found"'; then
            echo "❌ GitHub release not found for tag: $RELEASE_TAG"
            echo "Production deployments require a published GitHub release"
            echo "Create a release at: https://github.com/${{ github.repository }}/releases/new"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          echo "✅ GitHub release found for tag: $RELEASE_TAG"

          # Validate deployment reason is provided
          if [ -z "$DEPLOYMENT_REASON" ] || [ ${#DEPLOYMENT_REASON} -lt 10 ]; then
            echo "❌ Deployment reason is required and must be at least 10 characters"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate rollback plan is provided
          if [ -z "$ROLLBACK_PLAN" ] || [ ${#ROLLBACK_PLAN} -lt 20 ]; then
            echo "❌ Rollback plan is required and must be at least 20 characters"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set docker tag (default to release tag without 'v' prefix)
          if [ -z "$DOCKER_TAG" ]; then
            DOCKER_TAG=$(echo "$RELEASE_TAG" | sed 's/^v//')
            echo "🏷️ Using default docker tag: $DOCKER_TAG"
          fi

          # Determine application type based on project ID
          case "$PROJECT_ID" in
            *-react-frontend)
              APPLICATION_TYPE="react-frontend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-spring-backend)
              APPLICATION_TYPE="springboot-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-django-backend)
              APPLICATION_TYPE="django-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-nest-backend)
              APPLICATION_TYPE="nest-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *)
              APPLICATION_TYPE="web-app"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
          esac

          echo "✅ Production deployment validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "release-tag=$RELEASE_TAG" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT
          echo "deployment-reason=$DEPLOYMENT_REASON" >> $GITHUB_OUTPUT
          echo "rollback-plan=$ROLLBACK_PLAN" >> $GITHUB_OUTPUT

          echo "📋 Validated Production Deployment:"
          echo "  • Project: $PROJECT_ID"
          echo "  • Application Type: $APPLICATION_TYPE"
          echo "  • Release Tag: $RELEASE_TAG"
          echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
          echo "  • Authorized by: ${{ github.actor }}"

  production-environment-checks:
    needs: validate-production-request
    if: needs.validate-production-request.outputs.should-deploy == 'true'
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      checks-passed: ${{ steps.checks.outputs.passed }}
      staging-verified: ${{ steps.checks.outputs.staging-verified }}
    steps:
      - name: 🔍 Production Environment Pre-deployment Checks
        id: checks
        run: |
          echo "🔍 Running production environment pre-deployment checks..."
          
          PROJECT_ID="${{ needs.validate-production-request.outputs.project-id }}"
          DOCKER_IMAGE="${{ needs.validate-production-request.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-production-request.outputs.docker-tag }}"
          
          # Check if Docker image exists in registry
          echo "🐳 Checking if Docker image exists: $DOCKER_IMAGE:$DOCKER_TAG"
          
          # Install doctl if not available
          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
          fi
          
          # Authenticate with DigitalOcean
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"
          
          # Check if image exists in registry
          if doctl registry repository list-tags "$PROJECT_ID" --format Tag | grep -q "^$DOCKER_TAG$"; then
            echo "✅ Docker image found: $DOCKER_IMAGE:$DOCKER_TAG"
          else
            echo "❌ Docker image not found: $DOCKER_IMAGE:$DOCKER_TAG"
            echo "Available tags for $PROJECT_ID:"
            doctl registry repository list-tags "$PROJECT_ID" --format Tag | head -10
            echo "checks-passed=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # Check if same version is deployed in staging (recommended)
          echo "🎯 Checking if version is deployed in staging..."
          if [ -f "deployments/$PROJECT_ID/overlays/staging/kustomization.yaml" ]; then
            STAGING_IMAGE=$(grep -A 5 "images:" "deployments/$PROJECT_ID/overlays/staging/kustomization.yaml" | grep "newTag:" | awk '{print $2}' | tr -d '"' || echo "unknown")
            if [ "$STAGING_IMAGE" = "$DOCKER_TAG" ]; then
              echo "✅ Same version ($DOCKER_TAG) is deployed in staging"
              echo "staging-verified=true" >> $GITHUB_OUTPUT
            else
              echo "⚠️ Different version in staging: $STAGING_IMAGE vs production: $DOCKER_TAG"
              echo "staging-verified=false" >> $GITHUB_OUTPUT
              echo "Recommendation: Deploy to staging first for validation"
            fi
          else
            echo "⚠️ Staging deployment not found - cannot verify staging version"
            echo "staging-verified=false" >> $GITHUB_OUTPUT
          fi
          
          # Check production cluster connectivity
          echo "🔗 Checking production cluster connectivity..."
          PRODUCTION_CLUSTER_ID="${{ vars.PRODUCTION_CLUSTER_ID }}"
          if [ -n "$PRODUCTION_CLUSTER_ID" ]; then
            if doctl kubernetes cluster kubeconfig save "$PRODUCTION_CLUSTER_ID" >/dev/null 2>&1; then
              echo "✅ Production cluster is accessible"
            else
              echo "❌ Production cluster connectivity issues"
              echo "checks-passed=false" >> $GITHUB_OUTPUT
              exit 0
            fi
          else
            echo "⚠️ PRODUCTION_CLUSTER_ID not configured - using default cluster"
          fi
          
          echo "checks-passed=true" >> $GITHUB_OUTPUT
          echo "✅ All production environment checks passed"

  production-approval-gate:
    needs: [validate-production-request, production-environment-checks]
    if: needs.production-environment-checks.outputs.checks-passed == 'true'
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      approved: ${{ steps.approval.outputs.approved }}
    steps:
      - name: 🔒 Production Deployment Approval Gate
        id: approval
        run: |
          echo "🔒 Production deployment requires manual approval"
          echo ""
          echo "📋 Deployment Summary:"
          echo "  • Project: ${{ needs.validate-production-request.outputs.project-id }}"
          echo "  • Release Tag: ${{ needs.validate-production-request.outputs.release-tag }}"
          echo "  • Docker Image: ${{ needs.validate-production-request.outputs.docker-image }}:${{ needs.validate-production-request.outputs.docker-tag }}"
          echo "  • Authorized by: ${{ github.actor }}"
          echo "  • Staging Verified: ${{ needs.production-environment-checks.outputs.staging-verified }}"
          echo ""
          echo "📝 Deployment Reason:"
          echo "${{ needs.validate-production-request.outputs.deployment-reason }}"
          echo ""
          echo "🔄 Rollback Plan:"
          echo "${{ needs.validate-production-request.outputs.rollback-plan }}"
          echo ""
          echo "✅ Approval granted by environment protection rules"
          echo "approved=true" >> $GITHUB_OUTPUT

  deploy-to-production:
    needs: [validate-production-request, production-environment-checks, production-approval-gate]
    if: needs.production-approval-gate.outputs.approved == 'true'
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      deployment-success: ${{ steps.deploy.outputs.success }}
    steps:
      - name: 🚀 Deploy to Production Environment
        id: deploy
        run: |
          echo "🚀 Deploying to production environment..."
          
          PROJECT_ID="${{ needs.validate-production-request.outputs.project-id }}"
          APPLICATION_TYPE="${{ needs.validate-production-request.outputs.application-type }}"
          DOCKER_IMAGE="${{ needs.validate-production-request.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-production-request.outputs.docker-tag }}"
          RELEASE_TAG="${{ needs.validate-production-request.outputs.release-tag }}"
          
          echo "📋 Production Deployment Details:"
          echo "  • Project: $PROJECT_ID"
          echo "  • Application Type: $APPLICATION_TYPE"
          echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
          echo "  • Release Tag: $RELEASE_TAG"
          echo "  • Authorized by: ${{ github.actor }}"
          echo "  • Workflow Run: ${{ github.run_id }}"
          
          # Create production secrets
          echo "🔐 Preparing production secrets..."
          PRODUCTION_SECRETS=$(cat << EOF | jq -c .
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET_PRODUCTION }}",
            "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_PRODUCTION }}",
            "DB_HOST": "${{ secrets.DB_HOST_PRODUCTION }}",
            "DB_USER": "${{ secrets.DB_USER_PRODUCTION }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD_PRODUCTION }}",
            "DB_NAME": "${{ secrets.DB_NAME_PRODUCTION }}",
            "DB_PORT": "${{ secrets.DB_PORT_PRODUCTION }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_PRODUCTION }}",
            "SMTP_USER": "${{ secrets.SMTP_USER_PRODUCTION }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS_PRODUCTION }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_PRODUCTION }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_PRODUCTION }}"
          }
          EOF
          )
          
          # Base64 encode the secrets
          SECRETS_ENCODED=$(echo "$PRODUCTION_SECRETS" | base64 -w 0)
          
          # Create deployment payload
          PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "$PROJECT_ID",
            "project_id": "$PROJECT_ID",
            "application_type": "$APPLICATION_TYPE",
            "environment": "production",
            "docker_image": "$DOCKER_IMAGE",
            "docker_tag": "$DOCKER_TAG",
            "source_repo": "${{ github.repository }}",
            "source_branch": "main",
            "commit_sha": "${{ github.sha }}",
            "release_tag": "$RELEASE_TAG",
            "secrets_encoded": "$SECRETS_ENCODED",
            "deployment_trigger": "manual_production",
            "triggered_by": "${{ github.actor }}",
            "deployment_reason": "${{ needs.validate-production-request.outputs.deployment-reason }}",
            "rollback_plan": "${{ needs.validate-production-request.outputs.rollback-plan }}"
          }
          EOF
          )
          
          echo "📦 Production deployment payload:"
          echo "$PAYLOAD" | jq .
          
          # Trigger deployment via repository dispatch
          echo "🚀 Triggering GitOps production deployment..."
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITOPS_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            https://api.github.com/repos/${{ github.repository }}/dispatches \
            -d "{
              \"event_type\": \"deploy-to-argocd\",
              \"client_payload\": $PAYLOAD
            }"
          
          if [ $? -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Production deployment triggered successfully"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to trigger production deployment"
            exit 1
          fi

  notify-production-completion:
    needs: [validate-production-request, production-environment-checks, production-approval-gate, deploy-to-production]
    if: always()
    runs-on: [self-hosted, Linux]
    steps:
      - name: 🎉 Production Success Notification
        if: needs.deploy-to-production.outputs.deployment-success == 'true'
        run: |
          echo "🎉 Production deployment completed successfully!"
          echo ""
          echo "📊 Deployment Summary:"
          echo "  • Project: ${{ needs.validate-production-request.outputs.project-id }}"
          echo "  • Release Tag: ${{ needs.validate-production-request.outputs.release-tag }}"
          echo "  • Docker Image: ${{ needs.validate-production-request.outputs.docker-image }}:${{ needs.validate-production-request.outputs.docker-tag }}"
          echo "  • Authorized by: ${{ github.actor }}"
          echo "  • Workflow Run: ${{ github.run_id }}"
          echo "  • Staging Verified: ${{ needs.production-environment-checks.outputs.staging-verified }}"
          echo ""
          echo "📝 Deployment Reason:"
          echo "${{ needs.validate-production-request.outputs.deployment-reason }}"
          echo ""
          echo "🔄 Rollback Plan:"
          echo "${{ needs.validate-production-request.outputs.rollback-plan }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment in ArgoCD dashboard"
          echo "  • Run production smoke tests"
          echo "  • Verify application functionality"
          echo "  • Monitor application metrics and logs"
          echo "  • Update release notes and documentation"

      - name: ❌ Production Failure Notification
        if: failure() || needs.deploy-to-production.outputs.deployment-success != 'true'
        run: |
          echo "❌ Production deployment failed"
          echo ""
          echo "📊 Attempted Deployment:"
          echo "  • Project: ${{ needs.validate-production-request.outputs.project-id }}"
          echo "  • Release Tag: ${{ needs.validate-production-request.outputs.release-tag }}"
          echo "  • Docker Image: ${{ needs.validate-production-request.outputs.docker-image }}:${{ needs.validate-production-request.outputs.docker-tag }}"
          echo "  • Authorized by: ${{ github.actor }}"
          echo ""
          echo "🔄 Rollback Plan:"
          echo "${{ needs.validate-production-request.outputs.rollback-plan }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify Docker image exists in registry"
          echo "  • Ensure production secrets are properly configured"
          echo "  • Check production cluster connectivity"
          echo "  • Validate GitHub release exists for the tag"
          echo "  • Consider executing rollback plan if necessary"
