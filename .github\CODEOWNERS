# GitOps Code Ownership and Approval Requirements
# This file defines who must approve changes to specific parts of the GitOps repository
# 
# Documentation: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global fallback - repository administrators
* @AshrafSyed25

# =============================================================================
# PRODUCTION ENVIRONMENT PROTECTION
# =============================================================================
# All production deployment files require approval from senior team members
# This includes manifests, configurations, and any production-related changes

# Production overlay directories for all projects
deployments/*/overlays/production/ @AshrafSyed25
deployments/*/overlays/production/* @AshrafSyed25

# =============================================================================
# STAGING ENVIRONMENT PROTECTION
# =============================================================================
# Staging environment requires approval for quality assurance
# This ensures staging deployments are reviewed before production promotion

# Staging overlay directories for all projects
deployments/*/overlays/staging/ @AshrafSyed25
deployments/*/overlays/staging/* @AshrafSyed25

# =============================================================================
# CI/CD WORKFLOW PROTECTION
# =============================================================================
# Production deployment workflow requires approval from release manager
# This ensures only authorized personnel can modify production deployment logic

# Production deployment workflow
.github/workflows/prod-deploy.yml @AshrafSyed25

# Staging deployment workflow (for review and consistency)
.github/workflows/staging-deploy.yml @AshrafSyed25

# Core GitOps workflows that affect all environments
.github/workflows/deploy-from-cicd.yaml @AshrafSyed25
.github/workflows/promote-environment.yaml @AshrafSyed25

# =============================================================================
# INFRASTRUCTURE AND SECURITY
# =============================================================================
# Critical infrastructure files require approval

# GitHub Actions configuration and security
.github/workflows/ @AshrafSyed25

# ArgoCD configuration and RBAC
arc-config/ @AshrafSyed25

# Kubernetes manifests and templates
manifests/ @AshrafSyed25

# Deployment scripts that affect production
scripts/deploy.py @AshrafSyed25
scripts/promote-image.py @AshrafSyed25
scripts/generate_secrets.py @AshrafSyed25

# This CODEOWNERS file itself
.github/CODEOWNERS @AshrafSyed25


