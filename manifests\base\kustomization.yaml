apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID

resources:
- deployment.yaml
- service.yaml
- configmap.yaml

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEH<PERSON>DER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/managed-by: argocd
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  source.commit: PLACEHOLDER_COMMIT_SHA

namePrefix: ""
nameSuffix: ""

images:
- name: PLACEHOLDER_DOCKER_IMAGE
  newName: PLACEHOLDER_DOCKER_IMAGE

replicas:
- name: PLA<PERSON><PERSON><PERSON><PERSON><PERSON>_PROJECT_ID
  count: 1
