apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend-test
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: database-init
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: 2fa5c985
    app.kubernetes.io/managed-by: argocd
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          # Database connection details are already decoded by Kubernetes from secrets
          # No need for base64 decoding as Kubernetes handles this automatically
          echo "🔍 Checking database connectivity..."
          echo "Database Host: $DB_HOST"
          echo "Database Port: $DB_PORT"
          echo "Database User: $DB_USER"
          echo "Database Name: $DB_NAME"

          # Wait for database to be ready
          until pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER"; do
            echo "⏳ Waiting for database to be ready..."
            sleep 2
          done

          echo "✅ Database is ready!"

          # Optional: Test database connection
          if [ "$TEST_CONNECTION" = "true" ]; then
            echo "🧪 Testing database connection..."
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null
            if [ $? -eq 0 ]; then
              echo "✅ Database connection test successful!"
            else
              echo "❌ Database connection test failed!"
              exit 1
            fi
          fi
        env:
        # Database connection details from secrets (automatically decoded by Kubernetes)
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_USER
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_NAME
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_SSL_MODE
        # Application metadata
        - name: APP_NAME
          value: "ai-spring-backend-test"
        - name: PROJECT_ID
          value: "ai-spring-backend-test"
        - name: APPLICATION_TYPE
          value: "springboot-backend"
        - name: ENVIRONMENT
          value: "dev"  # This will be overridden by environment-specific patches
        # Optional: Enable connection testing
        - name: TEST_CONNECTION
          value: "false"  # Set to "true" to enable connection testing
