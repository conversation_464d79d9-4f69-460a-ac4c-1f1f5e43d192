name: 🎯 Staging Environment Deployment

on:
  workflow_dispatch:
    inputs:
      project_id:
        description: 'Project ID to deploy (e.g., ai-spring-backend)'
        required: true
        type: string
      git_tag:
        description: 'Git tag to deploy (semantic versioning required, e.g., v1.0.0)'
        required: true
        type: string
      docker_tag:
        description: 'Docker image tag to deploy (defaults to git tag without v prefix)'
        required: false
        type: string
      force_deploy:
        description: 'Force deployment even if validation fails'
        required: false
        type: boolean
        default: false

env:
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-staging-request:
    runs-on: [self-hosted, Linux]
    environment: staging
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      project-id: ${{ steps.validate.outputs.project-id }}
      git-tag: ${{ steps.validate.outputs.git-tag }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      application-type: ${{ steps.validate.outputs.application-type }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔍 Validate Staging Deployment Request
        id: validate
        run: |
          echo "=== STAGING DEPLOYMENT VALIDATION ==="
          echo "Triggered by: ${{ github.actor }}"
          echo "Project ID: ${{ github.event.inputs.project_id }}"
          echo "Git Tag: ${{ github.event.inputs.git_tag }}"
          echo "Docker Tag: ${{ github.event.inputs.docker_tag }}"
          echo "Force Deploy: ${{ github.event.inputs.force_deploy }}"
          echo "=================================="

          PROJECT_ID="${{ github.event.inputs.project_id }}"
          GIT_TAG="${{ github.event.inputs.git_tag }}"
          DOCKER_TAG="${{ github.event.inputs.docker_tag }}"
          FORCE_DEPLOY="${{ github.event.inputs.force_deploy }}"

          # Validate project ID format
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate git tag format (semantic versioning)
          if ! echo "$GIT_TAG" | grep -qE '^v?[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9.-]+)?$'; then
            echo "❌ Invalid git tag format: $GIT_TAG"
            echo "Git tag must follow semantic versioning (e.g., v1.0.0, 1.2.3, v2.0.0-beta.1)"
            if [ "$FORCE_DEPLOY" != "true" ]; then
              echo "should-deploy=false" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "⚠️ Force deploy enabled - proceeding despite invalid tag format"
            fi
          fi

          # Check if git tag exists
          if ! git tag -l | grep -q "^$GIT_TAG$"; then
            echo "❌ Git tag does not exist: $GIT_TAG"
            echo "Available tags:"
            git tag -l | tail -10
            if [ "$FORCE_DEPLOY" != "true" ]; then
              echo "should-deploy=false" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "⚠️ Force deploy enabled - proceeding despite missing tag"
            fi
          fi

          # Set docker tag (default to git tag without 'v' prefix)
          if [ -z "$DOCKER_TAG" ]; then
            DOCKER_TAG=$(echo "$GIT_TAG" | sed 's/^v//')
            echo "🏷️ Using default docker tag: $DOCKER_TAG"
          fi

          # Determine application type based on project ID
          case "$PROJECT_ID" in
            *-react-frontend)
              APPLICATION_TYPE="react-frontend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-spring-backend)
              APPLICATION_TYPE="springboot-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-django-backend)
              APPLICATION_TYPE="django-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *-nest-backend)
              APPLICATION_TYPE="nest-backend"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
            *)
              APPLICATION_TYPE="web-app"
              DOCKER_IMAGE="registry.digitalocean.com/doks-registry/$PROJECT_ID"
              ;;
          esac

          echo "✅ Staging deployment validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "git-tag=$GIT_TAG" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT

          echo "📋 Validated Deployment Details:"
          echo "  • Project: $PROJECT_ID"
          echo "  • Application Type: $APPLICATION_TYPE"
          echo "  • Git Tag: $GIT_TAG"
          echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"

  staging-environment-checks:
    needs: validate-staging-request
    if: needs.validate-staging-request.outputs.should-deploy == 'true'
    runs-on: [self-hosted, Linux]
    environment: staging
    outputs:
      checks-passed: ${{ steps.checks.outputs.passed }}
    steps:
      - name: 🔍 Staging Environment Pre-deployment Checks
        id: checks
        run: |
          echo "🔍 Running staging environment pre-deployment checks..."
          
          PROJECT_ID="${{ needs.validate-staging-request.outputs.project-id }}"
          DOCKER_IMAGE="${{ needs.validate-staging-request.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-staging-request.outputs.docker-tag }}"
          
          # Check if Docker image exists in registry
          echo "🐳 Checking if Docker image exists: $DOCKER_IMAGE:$DOCKER_TAG"
          
          # Install doctl if not available
          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
          fi
          
          # Authenticate with DigitalOcean
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"
          
          # Check if image exists in registry
          if doctl registry repository list-tags "$PROJECT_ID" --format Tag | grep -q "^$DOCKER_TAG$"; then
            echo "✅ Docker image found: $DOCKER_IMAGE:$DOCKER_TAG"
          else
            echo "❌ Docker image not found: $DOCKER_IMAGE:$DOCKER_TAG"
            echo "Available tags for $PROJECT_ID:"
            doctl registry repository list-tags "$PROJECT_ID" --format Tag | head -10
            echo "passed=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # Check staging cluster connectivity
          echo "🔗 Checking staging cluster connectivity..."
          STAGING_CLUSTER_ID="${{ vars.STAGING_CLUSTER_ID }}"
          if [ -n "$STAGING_CLUSTER_ID" ]; then
            if doctl kubernetes cluster kubeconfig save "$STAGING_CLUSTER_ID" >/dev/null 2>&1; then
              echo "✅ Staging cluster is accessible"
            else
              echo "⚠️ Staging cluster connectivity issues - deployment may fail"
            fi
          else
            echo "⚠️ STAGING_CLUSTER_ID not configured - using default cluster"
          fi
          
          echo "passed=true" >> $GITHUB_OUTPUT
          echo "✅ All staging environment checks passed"

  deploy-to-staging:
    needs: [validate-staging-request, staging-environment-checks]
    if: needs.staging-environment-checks.outputs.checks-passed == 'true'
    runs-on: [self-hosted, Linux]
    environment: staging
    outputs:
      deployment-success: ${{ steps.deploy.outputs.success }}
    steps:
      - name: 🚀 Deploy to Staging Environment
        id: deploy
        run: |
          echo "🚀 Deploying to staging environment..."
          
          PROJECT_ID="${{ needs.validate-staging-request.outputs.project-id }}"
          APPLICATION_TYPE="${{ needs.validate-staging-request.outputs.application-type }}"
          DOCKER_IMAGE="${{ needs.validate-staging-request.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-staging-request.outputs.docker-tag }}"
          GIT_TAG="${{ needs.validate-staging-request.outputs.git-tag }}"
          
          echo "📋 Staging Deployment Details:"
          echo "  • Project: $PROJECT_ID"
          echo "  • Application Type: $APPLICATION_TYPE"
          echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
          echo "  • Git Tag: $GIT_TAG"
          echo "  • Triggered by: ${{ github.actor }}"
          
          # Create staging secrets
          echo "🔐 Preparing staging secrets..."
          STAGING_SECRETS=$(cat << EOF | jq -c .
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET_STAGING }}",
            "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_STAGING }}",
            "DB_HOST": "${{ secrets.DB_HOST_STAGING }}",
            "DB_USER": "${{ secrets.DB_USER_STAGING }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD_STAGING }}",
            "DB_NAME": "${{ secrets.DB_NAME_STAGING }}",
            "DB_PORT": "${{ secrets.DB_PORT_STAGING }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_STAGING }}",
            "SMTP_USER": "${{ secrets.SMTP_USER_STAGING }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS_STAGING }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_STAGING }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_STAGING }}"
          }
          EOF
          )
          
          # Base64 encode the secrets
          SECRETS_ENCODED=$(echo "$STAGING_SECRETS" | base64 -w 0)
          
          # Create deployment payload
          PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "$PROJECT_ID",
            "project_id": "$PROJECT_ID",
            "application_type": "$APPLICATION_TYPE",
            "environment": "staging",
            "docker_image": "$DOCKER_IMAGE",
            "docker_tag": "$DOCKER_TAG",
            "source_repo": "${{ github.repository }}",
            "source_branch": "main",
            "commit_sha": "${{ github.sha }}",
            "git_tag": "$GIT_TAG",
            "secrets_encoded": "$SECRETS_ENCODED",
            "deployment_trigger": "manual_staging",
            "triggered_by": "${{ github.actor }}"
          }
          EOF
          )
          
          echo "📦 Staging deployment payload:"
          echo "$PAYLOAD" | jq .
          
          # Trigger deployment via repository dispatch
          echo "🚀 Triggering GitOps staging deployment..."
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITOPS_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            https://api.github.com/repos/${{ github.repository }}/dispatches \
            -d "{
              \"event_type\": \"deploy-to-argocd\",
              \"client_payload\": $PAYLOAD
            }"
          
          if [ $? -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Staging deployment triggered successfully"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to trigger staging deployment"
            exit 1
          fi

  notify-staging-completion:
    needs: [validate-staging-request, staging-environment-checks, deploy-to-staging]
    if: always()
    runs-on: [self-hosted, Linux]
    steps:
      - name: 🎉 Staging Success Notification
        if: needs.deploy-to-staging.outputs.deployment-success == 'true'
        run: |
          echo "🎉 Staging deployment completed successfully!"
          echo ""
          echo "📊 Deployment Summary:"
          echo "  • Project: ${{ needs.validate-staging-request.outputs.project-id }}"
          echo "  • Git Tag: ${{ needs.validate-staging-request.outputs.git-tag }}"
          echo "  • Docker Image: ${{ needs.validate-staging-request.outputs.docker-image }}:${{ needs.validate-staging-request.outputs.docker-tag }}"
          echo "  • Triggered by: ${{ github.actor }}"
          echo "  • Workflow Run: ${{ github.run_id }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment in ArgoCD dashboard"
          echo "  • Run staging environment tests"
          echo "  • Verify application functionality"
          echo "  • Ready for production promotion when validated"

      - name: ❌ Staging Failure Notification
        if: failure() || needs.deploy-to-staging.outputs.deployment-success != 'true'
        run: |
          echo "❌ Staging deployment failed"
          echo ""
          echo "📊 Attempted Deployment:"
          echo "  • Project: ${{ needs.validate-staging-request.outputs.project-id }}"
          echo "  • Git Tag: ${{ needs.validate-staging-request.outputs.git-tag }}"
          echo "  • Docker Image: ${{ needs.validate-staging-request.outputs.docker-image }}:${{ needs.validate-staging-request.outputs.docker-tag }}"
          echo "  • Triggered by: ${{ github.actor }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify Docker image exists in registry"
          echo "  • Ensure staging secrets are properly configured"
          echo "  • Check staging cluster connectivity"
          echo "  • Validate git tag exists and follows semantic versioning"
