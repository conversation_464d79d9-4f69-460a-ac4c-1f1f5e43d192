apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-spring-backend-test-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend-test-dev
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: 2fa5c985
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: springboot-backend
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-spring-backend-test-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-spring-backend-test/overlays/dev
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-spring-backend-test-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for ai-spring-backend-test"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting-ai-spring-backend"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "springboot-backend"
  - name: Source Branch
    value: "25-merge"
  - name: Commit SHA
    value: "2fa5c985"
  - name: Configuration
    value: "Development configuration with debug enabled"
